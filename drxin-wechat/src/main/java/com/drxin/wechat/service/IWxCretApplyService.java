package com.drxin.wechat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.wechat.domain.WxCretApply;
import com.drxin.wechat.vo.WxCretApplyVo;

import java.util.List;

public interface IWxCretApplyService extends IService<WxCretApply> {

    List<WxCretApply> selectCretApplyList(WxCretApplyVo apply);

    int insertCretApply(WxCretApply apply);

    int updateCretApply(WxCretApply apply);

    int submitCretApply(Long id);

    WxCretApply getApplyInfo(Long id);

    int passCretApply(Long[] ids, String type);

    int deleteCretApplyByIds(Long[] ids);

}
