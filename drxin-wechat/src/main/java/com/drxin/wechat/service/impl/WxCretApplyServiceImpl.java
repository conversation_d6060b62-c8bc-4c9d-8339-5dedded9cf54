package com.drxin.wechat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.system.service.IUploadRecordService;
import com.drxin.wechat.domain.WxCretApply;
import com.drxin.wechat.domain.WxUser;
import com.drxin.wechat.mapper.WxCretApplyMapper;
import com.drxin.wechat.service.IWxCretApplyService;
import com.drxin.wechat.vo.WxCretApplyVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Service
public class WxCretApplyServiceImpl extends ServiceImpl<WxCretApplyMapper, WxCretApply> implements IWxCretApplyService {

    @Resource
    private IUploadRecordService uploadRecordService;

    @Override
    public List<WxCretApply> selectCretApplyList(WxCretApplyVo apply) {
        // 当前用户是否为管理员或运营
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();
        if (currentUser.isAdmin() || currentUser.isOperator()) {
            return this.baseMapper.selectCretApplyList(apply);
        }
        apply.setCreateBy(SecurityUtils.getUsername());
        return baseMapper.selectCretApplyList(apply);
    }

    @Override
    public int insertCretApply(WxCretApply apply) {
        // 判断库里是否存在相同的申请
        QueryWrapper<WxCretApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id_card", apply.getIdCard());
        boolean exists = baseMapper.exists(queryWrapper);
        if (exists) {
            throw new ServiceException("该身份证号码已存在申请记录，请检查后重试。");
        }
        return baseMapper.insertCretApply(apply);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateCretApply(WxCretApply apply) {
        // 判断库里是否存在相同的申请
        QueryWrapper<WxCretApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id_card", apply.getIdCard());
        queryWrapper.ne("id", apply.getId());
        boolean exists = baseMapper.exists(queryWrapper);
        if (exists) {
            throw new ServiceException("该身份证号码已存在申请记录，请检查后重试。");
        }
        QueryWrapper<WxCretApply> fileChangeWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", apply.getId());
        queryWrapper.select("id","photo_id", "heimlichId", "paymentId", "examId");
        WxCretApply oldWxCretApply = baseMapper.selectOne(fileChangeWrapper);
        uploadRecordService.updateUploadRecordStatusToDelete(oldWxCretApply.getPhotoId(),apply.getPhotoId());
        uploadRecordService.updateUploadRecordStatusToDelete(oldWxCretApply.getHeimlichId(),apply.getHeimlichId());
        uploadRecordService.updateUploadRecordStatusToDelete(oldWxCretApply.getPaymentId(),apply.getPaymentId());
        uploadRecordService.updateUploadRecordStatusToDelete(oldWxCretApply.getExamId(),apply.getExamId());
        return baseMapper.updateCretApply(apply);
    }

    @Override
    public int submitCretApply(Long id) {
        UpdateWrapper<WxCretApply> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.set("apply_status", 2);
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public WxCretApply getApplyInfo(Long id) {
        return baseMapper.getApplyInfo(id);
    }

    @Override
    public int passCretApply(Long[] ids, String type) {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        List<Long> idList = Arrays.asList(ids);
        // 2. 根据 type 决定要设置的状态码
        int newStatus;
        switch (type) {
            case "pass":
                newStatus = 3;
                break;
            case "reject":
                newStatus = 4;
                break;
            default:
                throw new ServiceException("未识别的 type 值: " + type
                        + "，仅支持 \"pass\" 或 \"reject\"。");
        }

        // 3. 构造 LambdaUpdateWrapper，直接用实体字段引用，避免写死列名
        LambdaUpdateWrapper<WxCretApply> wrapper = new LambdaUpdateWrapper<WxCretApply>()
                .in(WxCretApply::getId, idList)
                .set(WxCretApply::getApplyStatus, newStatus);
        // 4. 执行更新，返回更新行数
        return baseMapper.update(null, wrapper);
    }

    @Override
    public int deleteCretApplyByIds(Long[] ids) {
        return baseMapper.deleteCretApplyByIds(ids);
    }
}
