package com.drxin.wechat.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("cret_apply")
public class WxCretApply extends BaseEntity {
    @TableId
    private Long id; // 主键

    private String name; // 姓名

    private String idCard; // 身份证号

    private String cardType; // 证件类型

    private String sex; // 性别

    private String phone; // 联系电话

    private String education; // 学历

    private String company; // 工作单位

    private String photoUrl; // 二寸照片 URL

    private Long photoId; // 二寸照片 ID

    private String heimlichUrl; // 海姆利克实操图 URL

    private Long heimlichId; // 海姆利克实操图 ID

    private String paymentUrl; // 付款凭证 URL

    private Long paymentId; // 付款凭证 ID

    private String examUrl; // 考试证明 URL

    private Long examId; // 考试证明 ID

    private ApplyType applyType; // 申请类型：SELF / OTHERS

    private Integer applyStatus; // 审批状态：1-新建，2-通过，3-拒绝

    private String receiveType; // 领取类型：SELF/MAIL

    private String receiveAddress; // 领取地址

    private Long addressId; // 地址ID

    private String practiceFlag; // 是否实操：Y/N

    public enum ApplyType {
        SELF,
        OTHERS
    }
}
