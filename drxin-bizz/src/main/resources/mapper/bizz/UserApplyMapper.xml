<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.drxin.bizz.mapper.UserApplyMapper">
    
    <resultMap type="com.drxin.bizz.domain.UserApply" id="UserApplyResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="applyStatus"    column="apply_status"    />
        <result property="userType"    column="user_type"    />
        <result property="realName"    column="real_name"    />
        <result property="cardType"    column="card_type"    />
        <result property="idCard"    column="id_card"    />
        <result property="sex"    column="sex"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="inviterId"    column="inviter_id"    />
        <result property="inviterName"    column="inviter_name"    />
        <result property="photoId"    column="photo_id"    />
        <result property="photoUrl"    column="photo_url"    />
        <result property="rejectReason"    column="reject_reason"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserApplyVo">
        select id, user_id, apply_status, user_type, real_name, card_type, id_card, sex, phone_number, inviter_id, inviter_name, photo_id, photo_url, reject_reason, create_by, create_time, update_by, update_time from user_apply
    </sql>

    <select id="selectUserApplyList" parameterType="com.drxin.bizz.domain.UserApply" resultMap="UserApplyResult">
        <include refid="selectUserApplyVo"/>
        <where>
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="applyStatus != null  and applyStatus != ''"> and apply_status = #{applyStatus}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="inviterName != null  and inviterName != ''"> and inviter_name like concat('%', #{inviterName}, '%')</if>
        </where>
        order by case when apply_status = '2' then 1 else 2 end, create_time desc
    </select>
    
    <select id="selectUserApplyById" parameterType="Long" resultMap="UserApplyResult">
        <include refid="selectUserApplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserApply" parameterType="com.drxin.bizz.domain.UserApply" useGeneratedKeys="true" keyProperty="id">
        insert into user_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="applyStatus != null">apply_status,</if>
            <if test="userType != null">user_type,</if>
            <if test="realName != null">real_name,</if>
            <if test="cardType != null">card_type,</if>
            <if test="idCard != null">id_card,</if>
            <if test="sex != null">sex,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="inviterId != null">inviter_id,</if>
            <if test="inviterName != null">inviter_name,</if>
            <if test="photoId != null">photo_id,</if>
            <if test="photoUrl != null">photo_url,</if>
            <if test="rejectReason != null">reject_reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="applyStatus != null">#{applyStatus},</if>
            <if test="userType != null">#{userType},</if>
            <if test="realName != null">#{realName},</if>
            <if test="cardType != null">#{cardType},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="sex != null">#{sex},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="inviterId != null">#{inviterId},</if>
            <if test="inviterName != null">#{inviterName},</if>
            <if test="photoId != null">#{photoId},</if>
            <if test="photoUrl != null">#{photoUrl},</if>
            <if test="rejectReason != null">#{rejectReason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUserApply" parameterType="com.drxin.bizz.domain.UserApply">
        update user_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="applyStatus != null">apply_status = #{applyStatus},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="cardType != null">card_type = #{cardType},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="inviterId != null">inviter_id = #{inviterId},</if>
            <if test="inviterName != null">inviter_name = #{inviterName},</if>
            <if test="photoId != null">photo_id = #{photoId},</if>
            <if test="photoUrl != null">photo_url = #{photoUrl},</if>
            <if test="rejectReason != null">reject_reason = #{rejectReason},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserApplyById" parameterType="Long">
        delete from user_apply where id = #{id}
    </delete>

    <delete id="deleteUserApplyByIds" parameterType="String">
        delete from user_apply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countPendingApplyByUserId" resultType="int">
        select count(1) from user_apply
        where user_id = #{userId}
        and apply_status = '2'
    </select>
</mapper>
