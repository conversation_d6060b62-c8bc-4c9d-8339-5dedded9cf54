package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.drxin.bizz.domain.AuditResult;
import com.drxin.bizz.domain.UserApply;

/**
 * 用户身份申请Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IUserApplyService extends IService<UserApply> {

    /**
     * 查询用户身份申请
     * 
     * @param id 用户身份申请主键
     * @return 用户身份申请
     */
    public UserApply selectUserApplyById(Long id);

    /**
     * 查询用户身份申请列表
     * 
     * @param userApply 用户身份申请
     * @return 用户身份申请集合
     */
    public List<UserApply> selectUserApplyList(UserApply userApply);

    /**
     * 新增用户身份申请
     * 
     * @param userApply 用户身份申请
     * @return 结果
     */
    public int insertUserApply(UserApply userApply);

    /**
     * 修改用户身份申请
     * 
     * @param userApply 用户身份申请
     * @return 结果
     */
    public int updateUserApply(UserApply userApply);

    /**
     * 批量删除用户身份申请
     * 
     * @param ids 需要删除的用户身份申请主键集合
     * @return 结果
     */
    public int deleteUserApplyByIds(Long[] ids);

    /**
     * 删除用户身份申请信息
     *
     * @param id 用户身份申请主键
     * @return 结果
     */
    public int deleteUserApplyById(Long id);

    /**
     * 审批用户身份申请（支持批量）
     *
     * @param ids 申请ID数组
     * @param type 审批类型 (pass-通过, reject-拒绝)
     * @return 审核结果对象，包含成功/失败统计和详细信息
     */
    public AuditResult auditUserApply(Long[] ids, String type);
}
