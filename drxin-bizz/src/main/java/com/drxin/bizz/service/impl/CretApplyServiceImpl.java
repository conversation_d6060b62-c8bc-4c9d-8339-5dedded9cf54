package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.domain.CretApply;
import com.drxin.bizz.mapper.CretApplyMapper;
import com.drxin.bizz.service.ICretApplyService;
import com.drxin.bizz.vo.CretApplyExportVo;
import com.drxin.bizz.vo.CretApplyVo;
import com.drxin.common.config.TencentCosConfig;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.core.domain.model.LoginUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.ExportUtils;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.common.utils.file.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 证件申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Slf4j
@Service
public class CretApplyServiceImpl extends ServiceImpl<CretApplyMapper, CretApply> implements ICretApplyService {
    @Resource
    private CretApplyMapper cretApplyMapper;

    @Resource
    private TencentCosConfig tencentCosConfig;

    /**
     * 查询证件申请
     *
     * @param id 证件申请主键
     * @return 证件申请
     */
    @Override
    public CretApply selectCretApplyById(Long id) {
        return cretApplyMapper.selectCretApplyById(id);
    }

    /**
     * 查询证件申请列表
     *
     * @param cretApply 证件申请
     * @return 证件申请
     */
    @Override
    public List<CretApply> selectCretApplyList(CretApplyVo cretApply) {
        // 当前用户是否为管理员或运营
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getUser();
        if (currentUser.isAdmin() || currentUser.isOperator()) {
            return this.baseMapper.selectCretApplyList(cretApply);
        }
        cretApply.setCreateBy(SecurityUtils.getUsername());
        return cretApplyMapper.selectCretApplyList(cretApply);
    }

    /**
     * 新增证件申请
     *
     * @param cretApply 证件申请
     * @return 结果
     */
    @Override
    public int insertCretApply(CretApply cretApply) {
        cretApply.setCreateTime(DateUtils.getNowDate());
        return cretApplyMapper.insertCretApply(cretApply);
    }

    /**
     * 修改证件申请
     *
     * @param cretApply 证件申请
     * @return 结果
     */
    @Override
    public int updateCretApply(CretApply cretApply) {
        cretApply.setUpdateTime(DateUtils.getNowDate());
        return cretApplyMapper.updateCretApply(cretApply);
    }

    /**
     * 批量删除证件申请
     *
     * @param ids 需要删除的证件申请主键
     * @return 结果
     */
    @Override
    public int deleteCretApplyByIds(Long[] ids) {
        return cretApplyMapper.deleteCretApplyByIds(ids);
    }

    /**
     * 删除证件申请信息
     *
     * @param id 证件申请主键
     * @return 结果
     */
    @Override
    public int deleteCretApplyById(Long id) {
        return cretApplyMapper.deleteCretApplyById(id);
    }

    @Override
    public int passCretApply(Long[] ids, String type) {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        List<Long> idList = Arrays.asList(ids);
        // 2. 根据 type 决定要设置的状态码
        int newStatus;
        switch (type) {
            case "pass":
                newStatus = 3;
                break;
            case "reject":
                newStatus = 4;
                break;
            default:
                throw new ServiceException("未识别的 type 值: " + type
                        + "，仅支持 \"pass\" 或 \"reject\"。");
        }

        // 3. 构造 LambdaUpdateWrapper，直接用实体字段引用，避免写死列名
        LambdaUpdateWrapper<CretApply> wrapper = new LambdaUpdateWrapper<CretApply>()
                .in(CretApply::getId, idList)
                .set(CretApply::getApplyStatus, newStatus);
        // 4. 执行更新，返回更新行数
        return baseMapper.update(null, wrapper);
    }

    @Override
    public void cretApplyExport(CretApplyVo cretApply, HttpServletResponse response) {
        String operationId = UUID.randomUUID().toString().substring(0, 8);
        log.info("开始导出证件申请数据，操作ID: {}", operationId);

        File tempDir = null;
        File zipFile = null;

        try {
            // 1. 参数验证
            validateExportRequest(cretApply);

            // 2. 查询导出数据
            List<CretApplyExportVo> exportData = baseMapper.selectCretApplyExportVoList(cretApply);
            if (exportData.isEmpty()) {
                throw new ServiceException("没有找到符合条件的数据");
            }

            // 3. 创建临时目录和文件名
            String fileName = ExportUtils.buildExportFileName(
                    cretApply.getBeginTime(),
                    cretApply.getEndTime(),
                    "证件申请导出"
            );
            tempDir = FileUtils.createTempDirectory(fileName);

            // 4. 生成Excel文件
            ExportUtils.generateExcelFile(
                    tempDir,
                    fileName,
                    exportData,
                    CretApplyExportVo.class,
                    "证件申请导出"
            );

            // 5. 准备照片下载任务
            List<ExportUtils.DownloadTask> downloadTasks = buildDownloadTasks(exportData, tempDir);

            // 6. 并行下载照片
            ExportUtils.downloadFilesParallel(downloadTasks);

            // 7. 创建ZIP压缩包
            zipFile = ExportUtils.createZipFile(tempDir, fileName);

            // 8. 输出到响应流
            ExportUtils.writeZipToResponse(zipFile, response);

            log.info("证件申请数据导出完成，操作ID: {}, 文件大小: {} KB",
                    operationId, zipFile.length() / 1024);

        } catch (Exception e) {
            log.error("导出证件申请数据失败，操作ID: {}", operationId, e);
            ExportUtils.handleExportError(response, e);
        } finally {
            // 清理资源
            ExportUtils.cleanupResources(tempDir, zipFile);
        }
    }

    /**
     * 验证导出请求参数
     */
    private void validateExportRequest(CretApplyVo cretApply) {
        if (cretApply == null) {
            throw new IllegalArgumentException("导出参数不能为空");
        }

        String beginTime = cretApply.getBeginTime();
        String endTime = cretApply.getEndTime();

        if (StringUtils.isBlank(beginTime) || StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }

        // 可以添加更多业务验证逻辑
        if (isTimeRangeExcessive(beginTime, endTime)) {
            throw new IllegalArgumentException("导出时间范围过大，请缩小时间范围");
        }
    }

    /**
     * 构建下载任务列表
     */
    private List<ExportUtils.DownloadTask> buildDownloadTasks(List<CretApplyExportVo> exportData, File tempDir) {
        return exportData.stream()
                .filter(vo -> StringUtils.isNotBlank(vo.getPhotoUrl()))
                .map(vo -> {
                    String fileName = buildPhotoFileName(vo);
                    String description = vo.getName() + "-" + vo.getIdCard();
                    return new ExportUtils.DownloadTask(getCosFileKey(vo.getPhotoUrl()), fileName, tempDir, description);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建照片文件名
     */
    private String buildPhotoFileName(CretApplyExportVo vo) {
        String name = ExportUtils.sanitizeFileName(vo.getName());
        String idCard = ExportUtils.sanitizeFileName(vo.getIdCard());
        return String.format("%s_%s.jpg", name, idCard);
    }

    // 获取cos文件key
    private String getCosFileKey(String fileUrl) {
        return fileUrl.replace(tencentCosConfig.getBaseUrl() + "/", "");
    }
    /**
     * 检查时间范围是否过大
     */
    private boolean isTimeRangeExcessive(String beginTime, String endTime) {
        // 实现时间范围检查逻辑，比如不超过3个月
        return false;
    }
}
