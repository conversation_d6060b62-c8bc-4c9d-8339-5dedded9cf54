package com.drxin.bizz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.vo.MemberUpdateTypeVo;

import java.util.List;

public interface IMemberInfoService extends IService<MemberInfo> {
    List<MemberInfo> selectMemberInfoList(MemberInfo memberInfo);

    MemberInfo selectMemberInfoById(Long id);

    int updateMemberType(MemberUpdateTypeVo memberUpdateTypeVo);

    List<MemberInfo> selectMemberInfoListToSelect(MemberUpdateTypeVo memberUpdateTypeVo);

    /**
     * 更新成员成交人
     * 注意：方法名为updateMemberInviter但实际更新的是成交人(deal_inviter_id)
     */
    int updateMemberInviter(String ids, String newInviterId);
}
