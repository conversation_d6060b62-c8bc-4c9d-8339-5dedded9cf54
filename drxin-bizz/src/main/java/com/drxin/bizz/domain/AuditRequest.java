package com.drxin.bizz.domain;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 审批请求对象
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class AuditRequest {
    
    /**
     * 申请ID数组
     */
    @NotNull(message = "申请ID列表不能为空")
    @NotEmpty(message = "申请ID列表不能为空")
    private Long[] ids;
    
    /**
     * 审批类型 (pass-通过, reject-拒绝)
     */
    @NotNull(message = "审批类型不能为空")
    private String type;
    
    /**
     * 驳回原因（当type为reject时使用）
     */
    private String rejectReason;

    public AuditRequest() {
    }

    public AuditRequest(Long[] ids, String type, String rejectReason) {
        this.ids = ids;
        this.type = type;
        this.rejectReason = rejectReason;
    }

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    @Override
    public String toString() {
        return "AuditRequest{" +
                "ids=" + java.util.Arrays.toString(ids) +
                ", type='" + type + '\'' +
                ", rejectReason='" + rejectReason + '\'' +
                '}';
    }
}
