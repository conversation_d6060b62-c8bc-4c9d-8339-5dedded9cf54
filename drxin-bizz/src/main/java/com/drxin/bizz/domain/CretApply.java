package com.drxin.bizz.domain;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 证件申请对象 cret_apply
 *
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("cret_apply")
public class CretApply extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCard;

    /**
     * 证件类型
     */
    @Excel(name = "证件类型")
    private String cardType;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phone;

    /**
     * 性别
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String sex;

    /**
     * 学历
     */
    @Excel(name = "学历")
    private String education;

    /**
     * 工作单位
     */
    @Excel(name = "工作单位")
    private String company;

    /**
     * 二寸照片URL
     */
    @Excel(name = "二寸照片URL")
    private String photoUrl;

    /**
     * 海姆利克实操照片
     */
    @Excel(name = "海姆利克实操照片")
    private String heimlichUrl;

    /**
     * 付款截图url
     */
    @Excel(name = "付款截图url")
    private String paymentUrl;

    /**
     * 考试截图
     */
    @Excel(name = "考试截图")
    private String examUrl;

    /**
     * 申请类型（SELF-本人申请,OTHERS-替他人申请）
     */
    @Excel(name = "申请类型", readConverterExp = "SELF=本人申请,OTHERS=替他人申请")
    private String applyType;

    /**
     * 审批状态（1-新建,2-审批中,3-通过,4拒绝）
     */
    @Excel(name = "审批状态", readConverterExp = "1=新建,2=审批中,3=通过,4=拒绝")
    private Long applyStatus;

    /**
     * 领取方式：SELF-自取,MAIL-邮寄
     */
    @Excel(name = "领取方式：SELF-自取,MAIL-邮寄", readConverterExp = "SELF=自取,MAIL=邮寄")
    private String receiveType;

    /**
     * 地址id
     */
    @Excel(name = "地址id")
    private String addressId;

    /**
     * 收件地址（快照）
     */
    @Excel(name = "收件地址")
    private String receiveAddress;

    /**
     * 备注
     */
    @Excel(name = "是否实操", readConverterExp = "Y=是,N=否")
    private String practiceFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("idCard", getIdCard())
                .append("cardType", getCardType())
                .append("phone", getPhone())
                .append("sex", getSex())
                .append("education", getEducation())
                .append("company", getCompany())
                .append("photoUrl", getPhotoUrl())
                .append("heimlichUrl", getHeimlichUrl())
                .append("paymentUrl", getPaymentUrl())
                .append("examUrl", getExamUrl())
                .append("applyType", getApplyType())
                .append("applyStatus", getApplyStatus())
                .append("receiveType", getReceiveType())
                .append("addressId", getAddressId())
                .append("receiveAddress", getReceiveAddress())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
