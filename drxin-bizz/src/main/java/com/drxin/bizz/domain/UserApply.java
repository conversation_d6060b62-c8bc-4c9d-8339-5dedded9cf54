package com.drxin.bizz.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.drxin.common.annotation.Excel;
import com.drxin.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
/**
 * 用户身份申请对象 user_apply
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserApply extends BaseEntity{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 申请状态 */
    @Excel(name = "申请状态")
    private String applyStatus;

    /** 用户身份 */
    @Excel(name = "用户身份")
    private String userType;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 证件类型 */
    @Excel(name = "证件类型")
    private String cardType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idCard;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 手机号码 */
    private String phoneNumber;

    /** 邀请人ID */
    @Excel(name = "邀请人ID")
    private Long inviterId;

    /** 邀请人姓名 */
    @Excel(name = "邀请人姓名")
    private String inviterName;

    /** 照片id */
    private Long photoId;

    /** 照片地址 */
    @Excel(name = "照片地址")
    private String photoUrl;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    private String rejectReason;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("applyStatus", getApplyStatus())
            .append("userType", getUserType())
            .append("realName", getRealName())
            .append("cardType", getCardType())
            .append("idCard", getIdCard())
            .append("sex", getSex())
            .append("phoneNumber", getPhoneNumber())
            .append("inviterId", getInviterId())
            .append("inviterName", getInviterName())
            .append("photoId", getPhotoId())
            .append("photoUrl", getPhotoUrl())
            .append("rejectReason", getRejectReason())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
