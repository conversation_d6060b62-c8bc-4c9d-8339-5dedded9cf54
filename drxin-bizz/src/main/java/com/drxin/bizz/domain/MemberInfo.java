package com.drxin.bizz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sys_user")
public class MemberInfo {
    // 用户Id
    @TableId
    private Long userId;

    private String nickName;

    // 姓名
    private String realName;
    // 性别
    private String sex;
    // 身份证号
    private String idCard;
    // 手机号
    @TableField(value = "phonenumber")
    private String phoneNumber;
    // 用户类型
    private String userType;
    // 邀请人
    private String inviter;
    // 邀请人Id
    private String inviterId;
    // 成交人
    private String dealInviter;
    // 成交人Id
    private Long dealInviterId;
    // 注册时间
    private Date registerTime;
    // 更新时间
    private Date updateTime;

}
