package com.drxin.bizz.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.AuditRequest;
import com.drxin.bizz.domain.AuditResult;
import com.drxin.bizz.domain.UserApply;
import com.drxin.bizz.service.IUserApplyService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 用户身份申请Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/bizz/user_apply")
public class UserApplyController extends BaseController {
    @Autowired
    private IUserApplyService userApplyService;

    /**
     * 查询用户身份申请列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserApply userApply){
        startPage();
        List<UserApply> list = userApplyService.selectUserApplyList(userApply);
        return getDataTable(list);
    }

    /**
     * 导出用户身份申请列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:export')")
    @Log(title = "用户身份申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserApply userApply) {
        List<UserApply> list = userApplyService.selectUserApplyList(userApply);
        ExcelUtil<UserApply> util = new ExcelUtil<UserApply>(UserApply.class);
        util.exportExcel(response, list, "用户身份申请数据");
    }

    /**
     * 获取用户身份申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(userApplyService.selectUserApplyById(id));
    }

    /**
     * 新增用户身份申请
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:add')")
    @Log(title = "用户身份申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserApply userApply) {
        return toAjax(userApplyService.insertUserApply(userApply));
    }

    /**
     * 修改用户身份申请
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:edit')")
    @Log(title = "用户身份申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserApply userApply) {
        return toAjax(userApplyService.updateUserApply(userApply));
    }

    /**
     * 删除用户身份申请
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:remove')")
    @Log(title = "用户身份申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userApplyService.deleteUserApplyByIds(ids));
    }

    /**
     * 审批用户身份申请（支持批量）
     */
    @PreAuthorize("@ss.hasPermi('bizz:user_apply:audit')")
    @Log(title = "用户身份申请审批", businessType = BusinessType.UPDATE)
    @GetMapping("/audit/{ids}")
    public AjaxResult audit(@PathVariable("ids") Long[] ids, String type) {
        try {
            AuditResult result = userApplyService.auditUserApply(ids, type);

            String actionName = "pass".equals(type) ? "审批通过" : "审批拒绝";
            String batchPrefix = ids.length == 1 ? "" : "批量";

            if (result.isAllSuccess()) {
                // 全部成功
                return AjaxResult.success(String.format("%s%s，成功处理%d条记录",
                    batchPrefix, actionName, result.getSuccessCount()));
            } else if (result.hasPartialSuccess()) {
                // 部分成功
                return AjaxResult.warn(String.format("%s%s部分成功：成功%d条，失败%d条。失败原因：%s",
                    batchPrefix, actionName, result.getSuccessCount(), result.getFailCount(), result.getErrorMessage()));
            } else {
                // 全部失败
                return AjaxResult.error(String.format("%s%s失败：%s",
                    batchPrefix, actionName, result.getErrorMessage()));
            }
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
