package com.drxin.bizz.controller;

import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.drxin.bizz.vo.CretApplyVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.drxin.common.annotation.Log;
import com.drxin.common.core.controller.BaseController;
import com.drxin.common.core.domain.AjaxResult;
import com.drxin.common.enums.BusinessType;
import com.drxin.bizz.domain.CretApply;
import com.drxin.bizz.service.ICretApplyService;
import com.drxin.common.utils.poi.ExcelUtil;
import com.drxin.common.core.page.TableDataInfo;

/**
 * 证件申请Controller
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@RestController
@RequestMapping("/bizz/apply")
public class CretApplyController extends BaseController {
    @Resource
    private ICretApplyService cretApplyService;

    /**
     * 查询证件申请列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:apply:list')")
    @GetMapping("/list")
    public TableDataInfo list(CretApplyVo cretApply){
        startPage();
        List<CretApply> list = cretApplyService.selectCretApplyList(cretApply);
        return getDataTable(list);
    }

    /**
     * 导出证件申请列表
     */
    @PreAuthorize("@ss.hasPermi('bizz:apply:export')")
    @Log(title = "证件申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CretApplyVo cretApply) {
        List<CretApply> list = cretApplyService.selectCretApplyList(cretApply);
        ExcelUtil<CretApply> util = new ExcelUtil<>(CretApply.class);
        util.exportExcel(response, list, "证件申请数据");
    }

    /**
     * 获取证件申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('bizz:apply:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(cretApplyService.selectCretApplyById(id));
    }

    /**
     * 新增证件申请
     */
    @PreAuthorize("@ss.hasPermi('bizz:apply:add')")
    @Log(title = "证件申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CretApply cretApply) {
        return toAjax(cretApplyService.insertCretApply(cretApply));
    }

    /**
     * 修改证件申请
     */
    @PreAuthorize("@ss.hasPermi('bizz:apply:edit')")
    @Log(title = "证件申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CretApply cretApply) {
        return toAjax(cretApplyService.updateCretApply(cretApply));
    }

    /**
     * 删除证件申请
     */
    @PreAuthorize("@ss.hasPermi('bizz:apply:remove')")
    @Log(title = "证件申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(cretApplyService.deleteCretApplyByIds(ids));
    }

    // 审核通过可批量
    @GetMapping("/audit/{ids}")
    public AjaxResult audit(@PathVariable("ids") Long[] ids, @RequestParam("type") String type) {
        cretApplyService.passCretApply(ids, type);
        return AjaxResult.success();
    }

    // 证件照信息导出
    @PostMapping("/exportCretInfo")
    public void exportCretInfo(HttpServletResponse response, CretApplyVo cretApply) throws IOException {
        cretApplyService.cretApplyExport(cretApply, response);
    }
}
